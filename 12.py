import tushare as ts
import pandas as pd

# 设置token
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
pro = ts.pro_api()

def clean_financial_data(df):
    """
    清理财务数据，去除重复记录
    对于同一报告期的多个公告，保留最新的公告数据
    """
    if df.empty:
        return df
    
    # 按股票代码、报告期、公告日期排序，保留最新公告
    df_sorted = df.sort_values(['ts_code', 'end_date', 'ann_date'], ascending=[True, True, False])
    
    # 去除同一股票、同一报告期的重复记录，保留第一条（即最新公告）
    df_cleaned = df_sorted.drop_duplicates(subset=['ts_code', 'end_date'], keep='first')
    
    return df_cleaned

def get_financial_data(ts_code, start_date, end_date):
    """
    获取企业财务数据（已去重）
    
    参数:
    ts_code: 股票代码，如'000001.SZ'
    start_date: 开始日期，格式'YYYYMMDD'
    end_date: 结束日期，格式'YYYYMMDD'
    """
    
    try:
        # 1. 获取现金流量表数据
        cashflow_data = pro.cashflow(ts_code=ts_code, 
                                    start_date=start_date, 
                                    end_date=end_date,
                                    fields='ts_code,ann_date,f_ann_date,end_date,n_cashflow_act')
        cashflow_data = clean_financial_data(cashflow_data)
        
        # 2. 获取利润表数据
        income_data = pro.income(ts_code=ts_code,
                                start_date=start_date,
                                end_date=end_date,
                                fields='ts_code,ann_date,f_ann_date,end_date,n_income')
        income_data = clean_financial_data(income_data)
        
        # 3. 获取资产负债表数据
        balance_data = pro.balancesheet(ts_code=ts_code,
                                       start_date=start_date,
                                       end_date=end_date,
                                       fields='ts_code,ann_date,f_ann_date,end_date,total_assets,total_hldr_eqy_exc_min_int,total_liab')
        balance_data = clean_financial_data(balance_data)
        
        return cashflow_data, income_data, balance_data
        
    except Exception as e:
        print(f"获取数据时出错: {e}")
        return None, None, None

def get_combined_financial_data(ts_code, start_date, end_date):
    """
    获取并合并所有财务指标数据（已去重）
    """
    try:
        # 获取各表数据
        cashflow_data = pro.cashflow(ts_code=ts_code, 
                                    start_date=start_date, 
                                    end_date=end_date,
                                    fields='ts_code,ann_date,end_date,n_cashflow_act')
        cashflow_data = clean_financial_data(cashflow_data)
        
        income_data = pro.income(ts_code=ts_code,
                                start_date=start_date,
                                end_date=end_date,
                                fields='ts_code,ann_date,end_date,n_income')
        income_data = clean_financial_data(income_data)
        
        balance_data = pro.balancesheet(ts_code=ts_code,
                                       start_date=start_date,
                                       end_date=end_date,
                                       fields='ts_code,ann_date,end_date,total_assets,total_hldr_eqy_exc_min_int,total_liab')
        balance_data = clean_financial_data(balance_data)
        
        # 检查数据是否为空
        if cashflow_data.empty and income_data.empty and balance_data.empty:
            print("所有财务数据都为空")
            return None
        
        # 逐步合并数据，使用inner join确保数据完整性
        merged_data = None
        
        # 从非空数据开始合并
        if not cashflow_data.empty:
            merged_data = cashflow_data[['ts_code', 'end_date', 'n_cashflow_act']].copy()
        
        if not income_data.empty:
            income_subset = income_data[['ts_code', 'end_date', 'n_income']].copy()
            if merged_data is None:
                merged_data = income_subset
            else:
                merged_data = pd.merge(merged_data, income_subset, on=['ts_code', 'end_date'], how='outer')
        
        if not balance_data.empty:
            balance_subset = balance_data[['ts_code', 'end_date', 'total_assets', 'total_hldr_eqy_exc_min_int', 'total_liab']].copy()
            if merged_data is None:
                merged_data = balance_subset
            else:
                merged_data = pd.merge(merged_data, balance_subset, on=['ts_code', 'end_date'], how='outer')
        
        if merged_data is None:
            return None
        
        # 重命名列名
        column_mapping = {
            'n_cashflow_act': '经营现金流净额',
            'n_income': '净利润',
            'total_assets': '总资产',
            'total_hldr_eqy_exc_min_int': '净资产',
            'total_liab': '总负债'
        }
        
        # 只重命名存在的列
        existing_columns = {k: v for k, v in column_mapping.items() if k in merged_data.columns}
        merged_data.rename(columns=existing_columns, inplace=True)
        
        # 按日期排序
        merged_data = merged_data.sort_values('end_date', ascending=False)
        
        # 最终去重检查（以防万一）
        merged_data = merged_data.drop_duplicates(subset=['ts_code', 'end_date'], keep='first')
        
        return merged_data
        
    except Exception as e:
        print(f"获取合并数据时出错: {e}")
        return None

def analyze_data_quality(df, data_name):
    """
    分析数据质量
    """
    if df is None or df.empty:
        print(f"{data_name}: 数据为空")
        return
    
    print(f"\n=== {data_name} 数据质量分析 ===")
    print(f"总记录数: {len(df)}")
    print(f"报告期范围: {df['end_date'].min()} 到 {df['end_date'].max()}")
    print(f"重复的报告期数量: {df.duplicated(subset=['ts_code', 'end_date']).sum()}")
    print(f"数据完整性:")
    for col in df.columns:
        if col not in ['ts_code', 'end_date', 'ann_date', 'f_ann_date']:
            null_count = df[col].isnull().sum()
            print(f"  {col}: {len(df) - null_count}/{len(df)} 条记录有数据")

# 使用示例
if __name__ == "__main__":
    # 以平安银行为例
    ts_code = '000001.SZ'
    start_date = '20200101'  # 建议设置具体日期
    end_date = ''
    
    print("正在获取财务数据...")
    
    # 获取分别的财务数据
    cashflow, income, balance = get_financial_data(ts_code, start_date, end_date)
    
    # 分析各数据表的质量
    analyze_data_quality(cashflow, "现金流量表")
    analyze_data_quality(income, "利润表")  
    analyze_data_quality(balance, "资产负债表")
    
    # 获取合并数据
    print("\n正在合并财务数据...")
    combined_data = get_combined_financial_data(ts_code, start_date, end_date)
    
    if combined_data is not None:
        analyze_data_quality(combined_data, "合并财务数据")
        
        print("\n=== 合并后的财务数据预览 ===")
        print(combined_data.head(10))
        
        # 保存到Excel
        try:
            combined_data.to_excel('financial_data_cleaned.xlsx', index=False)
            print(f"\n数据已保存到 financial_data_cleaned.xlsx 文件")
            print(f"共保存 {len(combined_data)} 条记录")
        except Exception as e:
            print(f"保存文件时出错: {e}")
    else:
        print("合并数据失败或数据为空")