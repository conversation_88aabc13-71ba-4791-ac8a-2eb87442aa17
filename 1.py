
import datetime
import pandas as pd
from xtquant import xtdata
import tushare as ts
token='2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
pro=ts.pro_api(token)
import os
from joblib import Parallel, delayed
import warnings
warnings.filterwarnings("ignore")

# dividend_type: 复权类型, none, front, back, front_ratio, back_ratio
dividend_type = 'front'
# period: 数据周期, 1m, 5m, 15m, 30m, 1h, 1d, tick
period = '5m'

# 目标时间点（9:35, 9:40, 9:45）
target_times = ['09:35:00', '09:40:00', '09:45:00']

def download_data(stock, period):
    try:
        xtdata.download_history_data(stock_code = stock, period = period)
    except:
        print(stock, 'data failed')

# 1 获取股票列表
print('获取股票列表...')
df = pro.daily_basic(ts_code='', trade_date='20241108')
stock_list = df['ts_code'].tolist()
print('获取股票列表完成')

# 2 下载数据
print('下载数据...')
res = Parallel(n_jobs=4)(delayed(download_data)(stock, period) for stock in stock_list)
print('下载数据完成')

# 3 整理数据，提取特定时间点的收盘价
print('整理数据...')
folder_path = 'data/' + period + '/' + dividend_type + '_specific_times'
if not os.path.exists(folder_path): os.makedirs(folder_path)

# 存储所有股票的特定时间点数据
all_data = []

for i, stock in enumerate(stock_list):
    # 隔50个打印下进度
    if i%50 == 0: print(i, stock)

    data = xtdata.get_local_data(field_list=['time', 'close'],
                                 stock_list=[stock],
                                 period=period,
                                 dividend_type=dividend_type)
    df = data[stock]

    try:
        # 转换时间戳为datetime
        df['datetime'] = df['time'].apply(lambda x: datetime.datetime.fromtimestamp(x/1000.0))
        df['time_str'] = df['datetime'].dt.strftime('%H:%M:%S')
        df['date'] = df['datetime'].dt.date

        # 筛选目标时间点的数据
        target_data = df[df['time_str'].isin(target_times)].copy()

        if not target_data.empty:
            target_data['code'] = stock
            # 重新排列列的顺序
            target_data = target_data[['code', 'datetime', 'date', 'time_str', 'close']]
            all_data.append(target_data)

            # 保存单个股票的数据
            target_data.to_csv(folder_path + '/' + stock + '_specific_times.csv', index=False)
    except Exception as e:
        print(f"处理股票 {stock} 时出错: {e}")
        pass

# 合并所有股票的数据并保存
if all_data:
    combined_df = pd.concat(all_data, ignore_index=True)
    combined_df = combined_df.sort_values(['date', 'time_str', 'code'])
    combined_df.to_csv(folder_path + '/all_stocks_specific_times.csv', index=False)
    print(f'已保存 {len(combined_df)} 条特定时间点数据')

print('数据整理完成')
  
    
    
