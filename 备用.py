import tushare as ts
import pandas as pd
import time
from datetime import datetime, timedelta

# 设置token
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
pro = ts.pro_api()

def clean_financial_data(df):
    """
    清理财务数据，去除重复记录
    对于同一报告期的多个公告，保留最新的公告数据
    """
    if df.empty:
        return df
    
    # 按股票代码、报告期、公告日期排序，保留最新公告
    df_sorted = df.sort_values(['ts_code', 'end_date', 'ann_date'], ascending=[True, True, False])
    
    # 去除同一股票、同一报告期的重复记录，保留第一条（即最新公告）
    df_cleaned = df_sorted.drop_duplicates(subset=['ts_code', 'end_date'], keep='first')
    
    return df_cleaned

def get_stock_basic_info(ts_code):
    """
    获取股票基本信息
    """
    try:
        stock_info = pro.stock_basic(ts_code=ts_code, fields='ts_code,symbol,name,area,industry,list_date')
        return stock_info.iloc[0] if not stock_info.empty else None
    except Exception as e:
        print(f"获取股票基本信息出错: {e}")
        return None

def get_trading_data(ts_code, start_date, end_date):
    """
    获取股票交易数据
    """
    try:
        trading_data = pro.daily(ts_code=ts_code, 
                               start_date=start_date, 
                               end_date=end_date,
                               fields='ts_code,trade_date,open,high,low,close,pre_close,vol,amount')
        
        # 获取流通市值和总市值数据
        basic_data = pro.daily_basic(ts_code=ts_code,
                                   start_date=start_date,
                                   end_date=end_date,
                                   fields='ts_code,trade_date,circ_mv,total_mv')
        
        # 合并数据
        if not trading_data.empty and not basic_data.empty:
            merged_trading = pd.merge(trading_data, basic_data, on=['ts_code', 'trade_date'], how='left')
        else:
            merged_trading = trading_data
            
        return merged_trading
        
    except Exception as e:
        print(f"获取交易数据出错: {e}")
        return None

def get_money_flow_data(ts_code, start_date, end_date):
    """
    获取资金流向数据
    """
    try:
        # 获取资金流向数据
        money_flow = pro.moneyflow(ts_code=ts_code,
                                 start_date=start_date,
                                 end_date=end_date,
                                 fields='ts_code,trade_date,buy_sm_amount,sell_sm_amount,buy_md_amount,sell_md_amount,buy_lg_amount,sell_lg_amount,buy_elg_amount,sell_elg_amount')
        
        return money_flow
        
    except Exception as e:
        print(f"获取资金流向数据出错: {e}")
        return None

def get_index_members():
    """
    获取主要指数成分股信息
    """
    index_codes = {
        '000300.SH': '沪深300',
        '000016.SH': '上证50',
        '000905.SH': '中证500',
        '000852.SH': '中证1000',
        '932000.CSI': '中证2000',
        '399006.SZ': '创业板指'
    }
    
    all_members = {}
    
    for index_code, index_name in index_codes.items():
        try:
            # 获取指数成分股
            members = pro.index_weight(index_code=index_code, fields='index_code,con_code,trade_date,weight')
            if not members.empty:
                # 获取最新日期的成分股
                latest_date = members['trade_date'].max()
                latest_members = members[members['trade_date'] == latest_date]
                all_members[index_name] = set(latest_members['con_code'].tolist())
            time.sleep(0.2)  # API限频
        except Exception as e:
            print(f"获取{index_name}成分股出错: {e}")
            all_members[index_name] = set()
    
    return all_members

def get_stock_sw_industry_optimized(ts_code):
    """
    优化后的申万行业分类获取函数
    使用 index_member_all 接口直接获取股票的申万行业分类信息
    """
    try:
        print(f"    获取{ts_code}的申万行业信息...")
        
        # 使用 index_member_all 接口获取申万行业分类
        df = pro.index_member_all(**{
            "l1_code": "",
            "l2_code": "",
            "l3_code": "",
            "is_new": "",
            "ts_code": ts_code,
            "src": "",
            "limit": "",
            "offset": ""
        }, fields=[
            "l1_code",
            "l1_name", 
            "l2_code",
            "l2_name",
            "l3_code",
            "l3_name",
            "ts_code",
            "name",
            "in_date",
            "out_date",
            "is_new"
        ])
        
        industry_info = {
            'sw_l1_code': '',
            'sw_l1_name': '',
            'sw_l2_code': '',
            'sw_l2_name': '',
            'sw_l3_code': '',
            'sw_l3_name': ''
        }
        
        if not df.empty:
            # 过滤出当前有效的行业分类（out_date为空或未来日期）
            current_date = datetime.now().strftime('%Y%m%d')
            
            # 筛选当前有效的分类
            valid_df = df[(df['out_date'].isna()) | (df['out_date'] == '') | (df['out_date'] >= current_date)]
            
            if not valid_df.empty:
                # 取第一条记录（通常股票只对应一个申万分类）
                record = valid_df.iloc[0]
                
                industry_info['sw_l1_code'] = record.get('l1_code', '') or ''
                industry_info['sw_l1_name'] = record.get('l1_name', '') or ''
                industry_info['sw_l2_code'] = record.get('l2_code', '') or ''
                industry_info['sw_l2_name'] = record.get('l2_name', '') or ''
                industry_info['sw_l3_code'] = record.get('l3_code', '') or ''
                industry_info['sw_l3_name'] = record.get('l3_name', '') or ''
                
                print(f"    申万一级: {industry_info['sw_l1_name']} ({industry_info['sw_l1_code']})")
                print(f"    申万二级: {industry_info['sw_l2_name']} ({industry_info['sw_l2_code']})")
                print(f"    申万三级: {industry_info['sw_l3_name']} ({industry_info['sw_l3_code']})")
            else:
                print(f"    未找到{ts_code}的有效申万行业分类")
        else:
            print(f"    {ts_code}无申万行业分类数据")
            
        return industry_info
        
    except Exception as e:
        print(f"    获取申万行业信息出错: {e}")
        return {
            'sw_l1_code': '',
            'sw_l1_name': '',
            'sw_l2_code': '',
            'sw_l2_name': '',
            'sw_l3_code': '',
            'sw_l3_name': ''
        }

def get_financial_data(ts_code, start_date, end_date):
    """
    获取企业财务数据（已去重）
    """
    
    try:
        # 1. 获取现金流量表数据
        cashflow_data = pro.cashflow(ts_code=ts_code, 
                                    start_date=start_date, 
                                    end_date=end_date,
                                    fields='ts_code,ann_date,f_ann_date,end_date,n_cashflow_act')
        cashflow_data = clean_financial_data(cashflow_data)
        
        # 2. 获取利润表数据
        income_data = pro.income(ts_code=ts_code,
                                start_date=start_date,
                                end_date=end_date,
                                fields='ts_code,ann_date,f_ann_date,end_date,n_income')
        income_data = clean_financial_data(income_data)
        
        # 3. 获取资产负债表数据
        balance_data = pro.balancesheet(ts_code=ts_code,
                                       start_date=start_date,
                                       end_date=end_date,
                                       fields='ts_code,ann_date,f_ann_date,end_date,total_assets,total_hldr_eqy_exc_min_int,total_liab')
        balance_data = clean_financial_data(balance_data)
        
        return cashflow_data, income_data, balance_data
        
    except Exception as e:
        print(f"获取财务数据时出错: {e}")
        return None, None, None

def get_comprehensive_stock_data(ts_code, start_date, end_date=None):
    """
    获取综合股票数据，包含所有要求的字段
    使用优化后的申万行业获取方法
    """
    if end_date is None:
        end_date = datetime.now().strftime('%Y%m%d')
    
    print(f"正在获取{ts_code}的综合数据...")
    
    # 1. 获取股票基本信息
    print("  获取基本信息...")
    stock_info = get_stock_basic_info(ts_code)
    
    # 2. 获取交易数据
    print("  获取交易数据...")
    trading_data = get_trading_data(ts_code, start_date, end_date)
    
    # 3. 获取资金流向数据
    print("  获取资金流向数据...")
    money_flow_data = get_money_flow_data(ts_code, start_date, end_date)
    
    # 4. 获取财务数据
    print("  获取财务数据...")
    cashflow_data, income, balance = get_financial_data(ts_code, start_date, end_date)
    
    # 5. 获取指数成分股信息
    print("  获取指数成分股信息...")
    index_members = get_index_members()
    
    # 6. 获取申万行业分类（使用优化后的方法）
    print("  获取申万行业分类...")
    industry_info = get_stock_sw_industry_optimized(ts_code)
    
    # 开始合并数据
    print("  合并数据...")
    
    if trading_data is None or trading_data.empty:
        print("交易数据为空，无法继续")
        return None
    
    # 以交易数据为基础
    result_data = trading_data.copy()
    
    # 添加股票名称
    if stock_info is not None:
        result_data['股票名称'] = stock_info['name']
    else:
        result_data['股票名称'] = ''
    
    # 重命名列名
    column_mapping = {
        'ts_code': '股票代码',
        'trade_date': '交易日期',
        'open': '开盘价',
        'high': '最高价',
        'low': '最低价',
        'close': '收盘价',
        'pre_close': '前收盘价',
        'vol': '成交量',
        'amount': '成交额',
        'circ_mv': '流通市值',
        'total_mv': '总市值'
    }
    
    result_data.rename(columns=column_mapping, inplace=True)
    
    # 合并资金流向数据
    if money_flow_data is not None and not money_flow_data.empty:
        money_flow_renamed = money_flow_data.rename(columns={
            'buy_sm_amount': '散户资金买入额',
            'sell_sm_amount': '散户资金卖出额',
            'buy_md_amount': '中户资金买入额',
            'sell_md_amount': '中户资金卖出额',
            'buy_lg_amount': '大户资金买入额',
            'sell_lg_amount': '大户资金卖出额',
            'buy_elg_amount': '机构资金买入额',
            'sell_elg_amount': '机构资金卖出额'
        })
        
        result_data = pd.merge(result_data, 
                             money_flow_renamed[['ts_code', 'trade_date', '中户资金买入额', '中户资金卖出额',
                                               '大户资金买入额', '大户资金卖出额', '散户资金买入额', '散户资金卖出额',
                                               '机构资金买入额', '机构资金卖出额']], 
                             left_on=['股票代码', '交易日期'], 
                             right_on=['ts_code', 'trade_date'], 
                             how='left')
        result_data.drop(['ts_code_y', 'trade_date_y'], axis=1, errors='ignore', inplace=True)
        result_data.rename(columns={'ts_code_x': '股票代码', 'trade_date_x': '交易日期'}, inplace=True)
    
    # 添加财务数据（取最近的季报数据）
    financial_columns = ['净资产', '总资产', '总负债', '净利润(当季)']
    for col in financial_columns:
        result_data[col] = None
    
    if balance is not None and not balance.empty:
        latest_balance = balance.iloc[0]
        result_data['净资产'] = latest_balance.get('total_hldr_eqy_exc_min_int')
        result_data['总资产'] = latest_balance.get('total_assets')
        result_data['总负债'] = latest_balance.get('total_liab')
    
    if income is not None and not income.empty:
        latest_income = income.iloc[0]
        result_data['净利润(当季)'] = latest_income.get('n_income')
    
    # 添加指数成分股标识
    index_columns = ['沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', '创业板指成分股']
    for col in index_columns:
        result_data[col] = 0
    
    for index_name, members in index_members.items():
        if ts_code in members:
            col_name = f"{index_name}成分股"
            if col_name in result_data.columns:
                result_data[col_name] = 1
    
    # 添加申万行业分类（使用优化后的数据）
    result_data['新版申万一级行业代码'] = industry_info.get('sw_l1_code', '')
    result_data['新版申万一级行业名称'] = industry_info.get('sw_l1_name', '')
    result_data['新版申万二级行业代码'] = industry_info.get('sw_l2_code', '')
    result_data['新版申万二级行业名称'] = industry_info.get('sw_l2_name', '')
    result_data['新版申万三级行业代码'] = industry_info.get('sw_l3_code', '')
    result_data['新版申万三级行业名称'] = industry_info.get('sw_l3_name', '')
    
    # 调整列顺序
    desired_columns = [
        '股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '前收盘价',
        '成交量', '成交额', '流通市值', '总市值', '净资产', '总资产', '总负债', '净利润(当季)',
        '中户资金买入额', '中户资金卖出额', '大户资金买入额', '大户资金卖出额',
        '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额',
        '沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', '创业板指成分股',
        '新版申万一级行业代码', '新版申万一级行业名称', '新版申万二级行业代码', '新版申万二级行业名称',
        '新版申万三级行业代码', '新版申万三级行业名称'
    ]
    
    # 确保所有列都存在，不存在的列填充为空值
    for col in desired_columns:
        if col not in result_data.columns:
            result_data[col] = None
    
    result_data = result_data[desired_columns]
    
    return result_data

def analyze_comprehensive_data(df, data_name):
    """
    分析综合数据质量
    """
    if df is None or df.empty:
        print(f"{data_name}: 数据为空")
        return
    
    print(f"\n=== {data_name} 数据质量分析 ===")
    print(f"总记录数: {len(df)}")
    print(f"交易日期范围: {df['交易日期'].min()} 到 {df['交易日期'].max()}")
    print(f"数据完整性:")
    
    key_columns = ['开盘价', '收盘价', '成交量', '成交额', '中户资金买入额', '净资产', 
                   '新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称']
    for col in key_columns:
        if col in df.columns:
            if '申万' in col:
                # 对于申万行业，检查非空且非空字符串的记录
                valid_count = df[col].notna().sum() - (df[col] == '').sum()
                print(f"  {col}: {valid_count}/{len(df)} 条记录有数据 ({valid_count/len(df)*100:.1f}%)")
                if valid_count > 0:
                    unique_industries = df[df[col].notna() & (df[col] != '')][col].unique()
                    print(f"    类别: {', '.join(unique_industries)}")
            else:
                null_count = df[col].isnull().sum()
                print(f"  {col}: {len(df) - null_count}/{len(df)} 条记录有数据 ({(len(df) - null_count)/len(df)*100:.1f}%)")

# 使用示例
if __name__ == "__main__":
    # 以平安银行为例
    ts_code = '000652.SZ'
    start_date = '20240101'  # 获取2024年的数据
    end_date = '20241231'
    
    print("正在获取综合股票数据...")
    
    # 获取综合数据
    comprehensive_data = get_comprehensive_stock_data(ts_code, start_date, end_date)
    
    if comprehensive_data is not None:
        analyze_comprehensive_data(comprehensive_data, "综合股票数据")
        
        print("\n=== 综合数据预览 ===")
        print(comprehensive_data.head(5))
        
        print(f"\n=== 申万行业信息检查 ===")
        if not comprehensive_data.empty:
            sw_l1_name = comprehensive_data['新版申万一级行业名称'].iloc[0]
            sw_l1_code = comprehensive_data['新版申万一级行业代码'].iloc[0]
            sw_l2_name = comprehensive_data['新版申万二级行业名称'].iloc[0]
            sw_l2_code = comprehensive_data['新版申万二级行业代码'].iloc[0]
            sw_l3_name = comprehensive_data['新版申万三级行业名称'].iloc[0]
            sw_l3_code = comprehensive_data['新版申万三级行业代码'].iloc[0]
            
            print(f"申万一级行业: {sw_l1_name} ({sw_l1_code})")
            print(f"申万二级行业: {sw_l2_name} ({sw_l2_code})")
            print(f"申万三级行业: {sw_l3_name} ({sw_l3_code})")

        print(f"\n=== 数据统计信息 ===")
        print(f"数据形状: {comprehensive_data.shape}")
        print(f"列名: {list(comprehensive_data.columns)}")

        # 检查申万行业数据质量
        for level in ['一级', '二级', '三级']:
            col_name = f'新版申万{level}行业名称'
            sw_data_quality = comprehensive_data[col_name].notna().sum() - (comprehensive_data[col_name] == '').sum()
            print(f"申万{level}行业数据完整性: {sw_data_quality}/{len(comprehensive_data)} ({sw_data_quality/len(comprehensive_data)*100:.1f}%)")
        
        # 保存到Excel
        try:
            filename = f'comprehensive_stock_data_{ts_code.replace(".", "_")}.xlsx'
            comprehensive_data.to_excel(filename, index=False)
            print(f"\n数据已保存到 {filename} 文件")
            print(f"共保存 {len(comprehensive_data)} 条记录，{len(comprehensive_data.columns)} 个字段")
            
            # 显示样本数据
            print(f"\n=== 样本数据展示 ===")
            pd.set_option('display.max_columns', None)
            pd.set_option('display.width', None)
            print(comprehensive_data.head(3))
            
        except Exception as e:
            print(f"保存文件时出错: {e}")
    else:
        print("获取综合数据失败")