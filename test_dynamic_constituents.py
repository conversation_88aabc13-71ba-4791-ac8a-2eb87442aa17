import pandas as pd
import tushare as ts
import time
from datetime import datetime

# 初始化pro接口
pro = ts.pro_api('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')

def test_constituents_api():
    """测试成分股API的基本功能"""
    print("=" * 60)
    print("测试成分股API功能")
    print("=" * 60)
    
    # 测试指数
    test_index = '000300.SH'  # 沪深300
    
    print(f"测试指数: {test_index}")
    
    try:
        # 1. 测试获取最新成分股
        print("\n1. 获取最新成分股...")
        latest_constituents = pro.index_weight(
            index_code=test_index,
            trade_date='20241220'  # 使用一个具体的交易日
        )
        
        if not latest_constituents.empty:
            print(f"   ✅ 获取成功，共{len(latest_constituents)}条记录")
            print(f"   列名: {list(latest_constituents.columns)}")
            print(f"   前5条记录:")
            print(latest_constituents.head())
        else:
            print("   ❌ 未获取到数据")
        
        # 2. 测试获取历史成分股变动
        print(f"\n2. 获取历史成分股变动...")
        historical_constituents = pro.index_weight(
            index_code=test_index,
            start_date='20240101',
            end_date='20241220'
        )
        
        if not historical_constituents.empty:
            print(f"   ✅ 获取成功，共{len(historical_constituents)}条记录")
            
            # 分析调整日期
            unique_dates = historical_constituents['trade_date'].unique()
            print(f"   调整日期数量: {len(unique_dates)}")
            print(f"   调整日期: {sorted(unique_dates)}")
            
            # 分析每个调整日期的成分股数量
            for date in sorted(unique_dates):
                date_data = historical_constituents[historical_constituents['trade_date'] == date]
                print(f"   {date}: {len(date_data)}只成分股")
                
        else:
            print("   ❌ 未获取到历史数据")
            
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def test_index_daily_api():
    """测试指数日线数据API"""
    print(f"\n3. 测试指数日线数据...")
    
    test_index = '000300.SH'
    
    try:
        df_daily = pro.index_daily(
            ts_code=test_index,
            start_date='20241201',
            end_date='20241220',
            fields=['ts_code', 'trade_date', 'open', 'close', 'pre_close', 'pct_chg']
        )
        
        if not df_daily.empty:
            print(f"   ✅ 获取成功，共{len(df_daily)}条记录")
            print(f"   时间范围: {df_daily['trade_date'].min()} 到 {df_daily['trade_date'].max()}")
            print(f"   前5条记录:")
            print(df_daily.head())
        else:
            print("   ❌ 未获取到日线数据")
            
    except Exception as e:
        print(f"   ❌ 获取日线数据失败: {e}")

def analyze_constituents_changes_simple():
    """简化版成分股变动分析"""
    print(f"\n4. 分析成分股变动...")
    
    test_index = '000300.SH'
    
    try:
        # 获取2024年的成分股变动
        df_constituents = pro.index_weight(
            index_code=test_index,
            start_date='20240101',
            end_date='20241220'
        )
        
        if df_constituents.empty:
            print("   ❌ 未获取到成分股数据")
            return
        
        # 按日期分组分析
        dates = sorted(df_constituents['trade_date'].unique())
        
        print(f"   2024年{test_index}成分股调整情况:")
        
        for i, date in enumerate(dates):
            date_constituents = df_constituents[df_constituents['trade_date'] == date]
            stock_codes = set(date_constituents['con_code'].tolist())
            
            print(f"   {date}: {len(stock_codes)}只成分股")
            
            if i > 0:
                # 与前一次调整比较
                prev_date = dates[i-1]
                prev_constituents = df_constituents[df_constituents['trade_date'] == prev_date]
                prev_stock_codes = set(prev_constituents['con_code'].tolist())
                
                added = stock_codes - prev_stock_codes
                removed = prev_stock_codes - stock_codes
                
                if added or removed:
                    print(f"     相比{prev_date}: 新增{len(added)}只, 移除{len(removed)}只")
                    if added:
                        print(f"     新增股票: {list(added)[:5]}{'...' if len(added) > 5 else ''}")
                    if removed:
                        print(f"     移除股票: {list(removed)[:5]}{'...' if len(removed) > 5 else ''}")
                else:
                    print(f"     相比{prev_date}: 无变动")
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")

def test_multiple_indices():
    """测试多个指数的成分股获取"""
    print(f"\n5. 测试多个指数...")
    
    indices = [
        ('000300.SH', '沪深300'),
        ('000016.SH', '上证50'),
        ('000905.SH', '中证500')
    ]
    
    for index_code, index_name in indices:
        print(f"\n   测试 {index_code} - {index_name}:")
        
        try:
            constituents = pro.index_weight(
                index_code=index_code,
                trade_date='20241220'
            )
            
            if not constituents.empty:
                print(f"     ✅ 成功获取{len(constituents)}只成分股")
            else:
                print(f"     ❌ 未获取到成分股")
                
        except Exception as e:
            print(f"     ❌ 获取失败: {e}")
        
        # 避免API限频
        time.sleep(0.5)

if __name__ == "__main__":
    print("开始测试动态成分股功能...")
    
    # 运行所有测试
    test_constituents_api()
    test_index_daily_api()
    analyze_constituents_changes_simple()
    test_multiple_indices()
    
    print(f"\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
