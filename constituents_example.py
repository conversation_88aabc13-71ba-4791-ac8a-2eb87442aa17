"""
成分股变动示例 - 展示实际的指数成分股变动情况
这个文件展示了为什么需要使用动态成分股
"""

# 沪深300指数成分股变动示例（简化版）
hs300_constituents_history = {
    # 2023年12月调整
    "20231215": [
        "000001.SZ", "000002.SZ", "000858.SZ", "002415.SZ", "002594.SZ",
        "600000.SH", "600036.SH", "600519.SH", "600887.SH", "601318.SH",
        "601398.SH", "601857.SH", "601988.SH", "000100.SZ", "000876.SZ",
        # ... 共300只股票
    ],
    
    # 2024年6月调整
    "20240614": [
        "000001.SZ", "000002.SZ", "000858.SZ", "002415.SZ", "002594.SZ",
        "600000.SH", "600036.SH", "600519.SH", "600887.SH", "601318.SH",
        "601398.SH", "601857.SH", "601988.SH", "000876.SZ",  # 移除了000100.SZ
        "688599.SH", "688981.SH",  # 新增科创板股票
        # ... 共300只股票
    ],
    
    # 2024年12月调整
    "20241213": [
        "000001.SZ", "000002.SZ", "000858.SZ", "002415.SZ", "002594.SZ",
        "600000.SH", "600036.SH", "600519.SH", "600887.SH", "601318.SH",
        "601398.SH", "601857.SH", "601988.SH", "000876.SZ",
        "688599.SH", "688981.SH", "301015.SZ", "301124.SZ",  # 新增创业板股票
        # ... 共300只股票
    ]
}

def demonstrate_constituents_impact():
    """演示成分股变动对策略的影响"""
    
    print("=" * 80)
    print("沪深300成分股变动影响演示")
    print("=" * 80)
    
    # 模拟交易日期
    trading_dates = [
        "20240101", "20240301", "20240601", "20240701", "20240901", "20241201"
    ]
    
    print("\n1. 使用固定成分股的问题（您当前的方法）:")
    print("   所有日期都使用 20241213 的成分股")
    
    fixed_constituents = hs300_constituents_history["20241213"]
    
    for date in trading_dates:
        print(f"   {date}: 使用2024年12月的300只成分股")
    
    print("\n   问题:")
    print("   - 2024年1月的数据却使用12月的成分股")
    print("   - 包含了当时还不在指数中的股票（如301015.SZ）")
    print("   - 可能包含已经被移除的股票")
    
    print("\n" + "-" * 60)
    
    print("\n2. 使用动态成分股的正确方法:")
    
    for date in trading_dates:
        # 找到该日期应该使用的成分股
        correct_constituents = get_constituents_for_date_demo(date)
        print(f"   {date}: 使用 {correct_constituents['date']} 调整的成分股")
        print(f"            包含股票: {len(correct_constituents['stocks'])}只")
        
        # 显示一些具体的股票变化
        if date >= "20240614":
            print(f"            新特征: 包含科创板股票 688599.SH, 688981.SH")
        if date >= "20241213":
            print(f"            新特征: 包含创业板股票 301015.SZ, 301124.SZ")
    
    print("\n" + "-" * 60)
    
    print("\n3. 具体变动分析:")
    
    dates = sorted(hs300_constituents_history.keys())
    
    for i, date in enumerate(dates):
        current_stocks = set(hs300_constituents_history[date])
        print(f"\n   {date} 调整:")
        print(f"   - 成分股数量: {len(current_stocks)}")
        
        if i > 0:
            prev_date = dates[i-1]
            prev_stocks = set(hs300_constituents_history[prev_date])
            
            added = current_stocks - prev_stocks
            removed = prev_stocks - current_stocks
            
            print(f"   - 相比 {prev_date}:")
            print(f"     新增: {len(added)}只 {list(added)[:3]}{'...' if len(added) > 3 else ''}")
            print(f"     移除: {len(removed)}只 {list(removed)[:3]}{'...' if len(removed) > 3 else ''}")
            
            # 分析新增股票的特点
            if added:
                analyze_added_stocks(added, date)

def get_constituents_for_date_demo(target_date):
    """演示版：根据日期获取对应的成分股"""
    
    # 找到小于等于目标日期的最大调整日期
    valid_dates = [date for date in hs300_constituents_history.keys() if date <= target_date]
    
    if valid_dates:
        latest_date = max(valid_dates)
        return {
            'date': latest_date,
            'stocks': hs300_constituents_history[latest_date]
        }
    else:
        # 如果没有找到，使用最早的
        earliest_date = min(hs300_constituents_history.keys())
        return {
            'date': earliest_date,
            'stocks': hs300_constituents_history[earliest_date]
        }

def analyze_added_stocks(added_stocks, date):
    """分析新增股票的特点"""
    
    analysis = {
        "20240614": "主要新增科创板优质股票，体现科技创新导向",
        "20241213": "新增创业板成长股，反映市场结构变化"
    }
    
    if date in analysis:
        print(f"     特点: {analysis[date]}")

def show_backtest_impact():
    """展示对回测的影响"""
    
    print("\n" + "=" * 80)
    print("对回测策略的影响分析")
    print("=" * 80)
    
    print("\n假设场景：2024年1月1日开始的策略回测")
    
    print("\n错误方法（固定成分股）:")
    print("- 使用2024年12月的300只成分股")
    print("- 包含301015.SZ（实际1月份还不在指数中）")
    print("- 可能导致收益率被高估或低估")
    print("- 无法反映真实的指数跟踪效果")
    
    print("\n正确方法（动态成分股）:")
    print("- 1月份使用2023年12月调整的成分股")
    print("- 6月份自动切换到2024年6月调整的成分股")
    print("- 12月份自动切换到2024年12月调整的成分股")
    print("- 真实反映指数的历史构成")
    
    print("\n预期影响:")
    print("- 收益率差异: 可能相差3-8%")
    print("- 夏普比率: 可能相差0.1-0.3")
    print("- 最大回撤: 可能相差2-5%")
    print("- 跟踪误差: 显著降低")

def generate_fix_code():
    """生成修复代码示例"""
    
    print("\n" + "=" * 80)
    print("修复代码示例")
    print("=" * 80)
    
    fix_code = '''
# 修复前（有问题的代码）
def get_index_constituents_old(index_code):
    trade_date = "20241231"  # 固定日期 - 问题所在！
    df_constituents = pro.index_weight(
        index_code=index_code,
        trade_date=trade_date
    )
    return df_constituents

# 修复后（正确的代码）
def get_index_constituents_new(index_code, start_date, end_date):
    # 获取历史成分股变动
    df_constituents = pro.index_weight(
        index_code=index_code,
        start_date=start_date,  # 不指定trade_date
        end_date=end_date
    )
    
    # 按日期分组
    constituents_by_date = {}
    for trade_date in df_constituents['trade_date'].unique():
        date_data = df_constituents[df_constituents['trade_date'] == trade_date]
        constituents_by_date[trade_date] = date_data['con_code'].tolist()
    
    return constituents_by_date

def get_constituents_for_date(constituents_history, target_date):
    # 找到该日期对应的成分股
    valid_dates = [d for d in constituents_history.keys() if d <= target_date]
    if valid_dates:
        latest_date = max(valid_dates)
        return constituents_history[latest_date]
    return []
'''
    
    print(fix_code)

if __name__ == "__main__":
    print("开始演示成分股变动的影响...")
    
    demonstrate_constituents_impact()
    show_backtest_impact()
    generate_fix_code()
    
    print("\n" + "=" * 80)
    print("总结:")
    print("1. 您的代码确实存在成分股变动问题")
    print("2. 这会显著影响回测结果的准确性")
    print("3. 建议尽快采用动态成分股方案")
    print("4. 修复后的策略表现会更真实可靠")
    print("=" * 80)
