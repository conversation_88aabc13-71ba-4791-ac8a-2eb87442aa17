import pandas as pd
import tushare as ts
import time
from datetime import datetime, timedelta
import os

# 初始化pro接口
pro = ts.pro_api('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')

def get_historical_constituents(index_code, start_date='20070101', end_date=None):
    """
    获取指数的历史成分股变动数据
    返回每个调整日期的成分股列表
    """
    if end_date is None:
        end_date = datetime.now().strftime('%Y%m%d')
    
    print(f"   获取{index_code}的历史成分股变动...")
    
    try:
        # 获取指数成分股历史数据
        df_constituents = pro.index_weight(
            index_code=index_code,
            start_date=start_date,
            end_date=end_date
        )
        
        if df_constituents.empty:
            print(f"   ⚠️ 未获取到{index_code}的成分股数据")
            return {}
        
        # 按交易日期分组，获取每个调整日期的成分股
        constituents_by_date = {}
        
        for trade_date in df_constituents['trade_date'].unique():
            date_constituents = df_constituents[df_constituents['trade_date'] == trade_date]
            stock_codes = []
            
            # 获取股票代码列
            stock_code_column = None
            possible_columns = ['con_code', 'ts_code', 'code', 'stock_code']
            for col in possible_columns:
                if col in date_constituents.columns:
                    stock_code_column = col
                    break
            
            if stock_code_column:
                for code in date_constituents[stock_code_column].dropna().unique():
                    formatted_code = convert_stock_code_to_format(str(code))
                    stock_codes.append(formatted_code)
                
                stock_codes.sort()
                constituents_by_date[trade_date] = ' '.join(stock_codes)
        
        print(f"   ✅ 获取到{len(constituents_by_date)}个调整日期的成分股数据")
        return constituents_by_date
        
    except Exception as e:
        print(f"   ❌ 获取历史成分股失败: {e}")
        return {}

def get_constituents_for_date(constituents_history, target_date):
    """
    根据目标日期获取对应的成分股
    使用最近的历史调整日期的成分股
    """
    if not constituents_history:
        return ""
    
    # 将目标日期转换为字符串格式
    target_date_str = str(target_date)
    
    # 找到小于等于目标日期的最大调整日期
    valid_dates = [date for date in constituents_history.keys() if date <= target_date_str]
    
    if valid_dates:
        latest_date = max(valid_dates)
        return constituents_history[latest_date]
    else:
        # 如果没有找到，使用最早的调整日期
        earliest_date = min(constituents_history.keys())
        return constituents_history[earliest_date]

def convert_stock_code_to_format(ts_code):
    """
    将股票代码转换为要求的格式
    例: 600000.SH -> sh600000, 000001.SZ -> sz000001
    """
    if '.' in ts_code:
        code, exchange = ts_code.split('.')
        if exchange == 'SZ':
            return f"sz{code}"
        elif exchange == 'SH':
            return f"sh{code}"
        elif exchange == 'BJ':
            return f"bj{code}"
        else:
            return f"{exchange.lower()}{code}"
    else:
        return ts_code.lower()

def convert_index_code_to_filename_format(index_code):
    """
    将指数代码转换为文件名格式
    例: 000001.SH -> sh000001, 399006.SZ -> sz399006
    """
    if '.' in index_code:
        code, exchange = index_code.split('.')
        if exchange == 'SZ':
            return f"sz{code}"
        elif exchange == 'SH':
            return f"sh{code}"
        elif exchange == 'CSI':
            return f"csi{code}"
        else:
            return f"{exchange.lower()}{code}"
    else:
        return index_code.lower()

def process_index_data_with_dynamic_constituents(index_code, start_date='20070101', end_date=None):
    """
    处理指数数据，使用动态成分股
    """
    if end_date is None:
        end_date = datetime.now().strftime('%Y%m%d')
    
    print(f"🔍 开始处理{index_code}的动态成分股数据...")
    
    # 1. 获取指数历史成分股变动
    constituents_history = get_historical_constituents(index_code, start_date, end_date)
    
    # 2. 获取指数日线数据
    print(f"   获取{index_code}的日线数据...")
    try:
        df_daily = pro.index_daily(
            ts_code=index_code,
            start_date=start_date,
            end_date=end_date,
            fields=['ts_code', 'trade_date', 'open', 'close', 'pre_close', 'pct_chg']
        )
        
        if df_daily.empty:
            print(f"   ❌ 未获取到{index_code}的日线数据")
            return pd.DataFrame()
        
        print(f"   ✅ 获取到{len(df_daily)}条日线数据")
        
    except Exception as e:
        print(f"   ❌ 获取日线数据失败: {e}")
        return pd.DataFrame()
    
    # 3. 按日期排序
    df_daily = df_daily.sort_values('trade_date', ascending=True).reset_index(drop=True)
    
    # 4. 处理每日数据，匹配对应的成分股
    output_data = []
    equity_curve_value = 1.0
    benchmark_value = 1.0
    
    for i, row in df_daily.iterrows():
        row_data = {}
        trade_date = row['trade_date']
        
        # 基础信息
        row_data['交易日期'] = trade_date
        row_data['策略名称'] = convert_index_code_to_filename_format(index_code)
        
        # 获取当日对应的成分股
        constituents_str = get_constituents_for_date(constituents_history, trade_date)
        row_data['持有股票代码'] = constituents_str
        
        # 计算涨跌幅
        pct_change = row.get('pct_chg')
        if pd.isna(pct_change) and i > 0:
            # 如果API没有返回涨跌幅，从价格计算
            current_close = row.get('close')
            prev_close = df_daily.iloc[i-1].get('close')
            
            if pd.notna(current_close) and pd.notna(prev_close) and prev_close != 0:
                pct_change = ((current_close - prev_close) / prev_close) * 100
        
        row_data['涨跌幅'] = round(pct_change, 4) if pd.notna(pct_change) else None
        row_data['指数涨跌幅'] = round(pct_change, 4) if pd.notna(pct_change) else None
        
        # 计算开盘买入涨跌幅
        open_price = row.get('open')
        close_price = row.get('close')
        
        if pd.notna(open_price) and pd.notna(close_price) and open_price != 0:
            open_to_close_pct = ((close_price - open_price) / open_price) * 100
            row_data['开盘买入涨跌幅'] = round(open_to_close_pct, 4)
        else:
            row_data['开盘买入涨跌幅'] = None
        
        # 计算权益曲线
        if i == 0:
            row_data['equity_curve'] = 1.0
        else:
            open_buy_pct = row_data.get('开盘买入涨跌幅')
            if pd.notna(open_buy_pct):
                equity_curve_value = equity_curve_value * (1 + open_buy_pct / 100)
            row_data['equity_curve'] = round(equity_curve_value, 6)
        
        # 计算基准
        if i == 0:
            row_data['benchmark'] = 1.0
        else:
            if pd.notna(pct_change):
                benchmark_value = benchmark_value * (1 + pct_change / 100)
            row_data['benchmark'] = round(benchmark_value, 6)
        
        output_data.append(row_data)
    
    # 创建DataFrame
    if output_data:
        df_result = pd.DataFrame(output_data)
        
        # 调整列顺序
        desired_order = [
            '交易日期', '策略名称', '持有股票代码', '涨跌幅', 
            '开盘买入涨跌幅', 'equity_curve', '指数涨跌幅', 'benchmark'
        ]
        
        df_result = df_result[desired_order]
        
        print(f"   ✅ 处理完成，共{len(df_result)}条记录")
        return df_result
    else:
        return pd.DataFrame()

def save_data_with_dynamic_constituents(index_code, df_data):
    """
    保存动态成分股数据
    """
    if df_data.empty:
        return False
    
    try:
        # 创建输出目录
        output_dir = "stock-ind-element-equity-dynamic"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 文件名
        index_code_format = convert_index_code_to_filename_format(index_code)
        filename = f"{index_code_format}_dynamic.csv"
        filepath = os.path.join(output_dir, filename)
        
        # 保存文件
        with open(filepath, 'w', encoding='gbk') as f:
            # 第一行：空行
            f.write(',' * (len(df_data.columns) - 1) + '\n')
            # 第二行：列名
            f.write(','.join(df_data.columns) + '\n')
            # 数据行
            for _, row in df_data.iterrows():
                row_values = []
                for col in df_data.columns:
                    value = row[col]
                    if pd.isna(value):
                        row_values.append('')
                    else:
                        row_values.append(str(value))
                f.write(','.join(row_values) + '\n')
        
        print(f"   ✅ 数据已保存到: {filepath}")
        return True
        
    except Exception as e:
        print(f"   ❌ 保存失败: {e}")
        return False

def analyze_constituents_changes(constituents_history):
    """
    分析成分股变动情况
    """
    if not constituents_history:
        return
    
    print(f"\n📊 成分股变动分析:")
    print(f"   调整次数: {len(constituents_history)}")
    
    dates = sorted(constituents_history.keys())
    for i, date in enumerate(dates):
        constituents = constituents_history[date].split()
        print(f"   {date}: {len(constituents)}只成分股")
        
        if i > 0:
            prev_constituents = set(constituents_history[dates[i-1]].split())
            curr_constituents = set(constituents)
            
            added = curr_constituents - prev_constituents
            removed = prev_constituents - curr_constituents
            
            if added or removed:
                print(f"     新增: {len(added)}只, 移除: {len(removed)}只")

def main():
    """
    主函数：处理动态成分股数据
    """
    print("=" * 80)
    print("🚀 指数动态成分股数据处理")
    print("=" * 80)
    
    # 主要指数列表
    indices = [
        ('000001.SH', '上证指数'),
        ('000016.SH', '上证50'),
        ('000300.SH', '沪深300'),
        ('000852.SH', '中证1000'),
        ('000905.SH', '中证500'),
        ('932000.CSI', '中证2000'),
        ('399006.SZ', '创业板指')
    ]
    
    print("请选择要处理的指数:")
    for i, (code, name) in enumerate(indices, 1):
        print(f"{i}. {code} - {name}")
    print("8. 处理所有指数")
    
    try:
        choice = input("请输入选择 (1-8): ").strip()
        choice_num = int(choice)
    except:
        choice_num = 1
    
    if 1 <= choice_num <= 7:
        # 处理单个指数
        index_code, index_name = indices[choice_num - 1]
        print(f"\n🎯 处理指数: {index_code} - {index_name}")
        
        df_result = process_index_data_with_dynamic_constituents(index_code)
        
        if not df_result.empty:
            save_success = save_data_with_dynamic_constituents(index_code, df_result)
            
            if save_success:
                print(f"\n🎉 {index_name} 处理完成!")
                print(f"📊 记录数: {len(df_result)}")
                print(f"📅 时间范围: {df_result['交易日期'].min()} 到 {df_result['交易日期'].max()}")
            else:
                print(f"\n❌ {index_name} 保存失败")
        else:
            print(f"\n❌ {index_name} 处理失败")
    
    elif choice_num == 8:
        # 处理所有指数
        print(f"\n🎯 处理所有指数...")
        
        for index_code, index_name in indices:
            print(f"\n📊 处理: {index_code} - {index_name}")
            
            df_result = process_index_data_with_dynamic_constituents(index_code)
            
            if not df_result.empty:
                save_success = save_data_with_dynamic_constituents(index_code, df_result)
                
                if save_success:
                    print(f"   ✅ {index_name} 完成")
                else:
                    print(f"   ❌ {index_name} 保存失败")
            else:
                print(f"   ❌ {index_name} 处理失败")
            
            # 添加延时避免API限制
            time.sleep(2)
    
    print(f"\n🎊 程序执行完成!")
    print("=" * 80)

if __name__ == "__main__":
    main()
