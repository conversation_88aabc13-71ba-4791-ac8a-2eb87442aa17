import tushare as ts
import pandas as pd
import time
from datetime import datetime, timedelta

# 设置token
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
pro = ts.pro_api()

def clean_financial_data(df):
    """
    清理财务数据，去除重复记录
    对于同一报告期的多个公告，保留最新的公告数据
    """
    if df.empty:
        return df
    
    # 按股票代码、报告期、公告日期排序，保留最新公告
    df_sorted = df.sort_values(['ts_code', 'end_date', 'ann_date'], ascending=[True, True, False])
    
    # 去除同一股票、同一报告期的重复记录，保留第一条（即最新公告）
    df_cleaned = df_sorted.drop_duplicates(subset=['ts_code', 'end_date'], keep='first')
    
    return df_cleaned

def get_stock_basic_info(ts_code):
    """
    获取股票基本信息
    """
    try:
        stock_info = pro.stock_basic(ts_code=ts_code, fields='ts_code,symbol,name,area,industry,list_date')
        return stock_info.iloc[0] if not stock_info.empty else None
    except Exception as e:
        print(f"获取股票基本信息出错: {e}")
        return None

def get_trading_data(ts_code, start_date, end_date):
    """
    获取股票交易数据
    """
    try:
        trading_data = pro.daily(ts_code=ts_code, 
                               start_date=start_date, 
                               end_date=end_date,
                               fields='ts_code,trade_date,open,high,low,close,pre_close,vol,amount')
        
        # 获取流通市值和总市值数据
        basic_data = pro.daily_basic(ts_code=ts_code,
                                   start_date=start_date,
                                   end_date=end_date,
                                   fields='ts_code,trade_date,circ_mv,total_mv')
        
        # 合并数据
        if not trading_data.empty and not basic_data.empty:
            merged_trading = pd.merge(trading_data, basic_data, on=['ts_code', 'trade_date'], how='left')
        else:
            merged_trading = trading_data
            
        return merged_trading
        
    except Exception as e:
        print(f"获取交易数据出错: {e}")
        return None

def get_money_flow_data(ts_code, start_date, end_date):
    """
    获取资金流向数据
    """
    try:
        # 获取资金流向数据
        money_flow = pro.moneyflow(ts_code=ts_code,
                                 start_date=start_date,
                                 end_date=end_date,
                                 fields='ts_code,trade_date,buy_sm_amount,sell_sm_amount,buy_md_amount,sell_md_amount,buy_lg_amount,sell_lg_amount,buy_elg_amount,sell_elg_amount')
        
        return money_flow
        
    except Exception as e:
        print(f"获取资金流向数据出错: {e}")
        return None

def get_index_members():
    """
    获取主要指数成分股信息
    """
    index_codes = {
        '000300.SH': '沪深300',
        '000016.SH': '上证50',
        '000905.SH': '中证500',
        '000852.SH': '中证1000',
        '932000.CSI': '中证2000',
        '399006.SZ': '创业板指'
    }
    
    all_members = {}
    
    for index_code, index_name in index_codes.items():
        try:
            # 获取指数成分股
            members = pro.index_weight(index_code=index_code, fields='index_code,con_code,trade_date,weight')
            if not members.empty:
                # 获取最新日期的成分股
                latest_date = members['trade_date'].max()
                latest_members = members[members['trade_date'] == latest_date]
                all_members[index_name] = set(latest_members['con_code'].tolist())
            time.sleep(0.2)  # API限频
        except Exception as e:
            print(f"获取{index_name}成分股出错: {e}")
            all_members[index_name] = set()
    
    return all_members

def get_sw_industry_classification():
    """
    获取申万行业分类表（2021版）
    返回包含一级、二级、三级分类的完整表
    """
    try:
        print("  尝试获取申万行业分类...")
        # 获取申万2021版行业分类
        # 需要2000积分权限
        sw_class = pro.index_classify(level='L1', src='SW2021')  # 一级分类
        print(f"  申万一级分类获取成功，共{len(sw_class)}个行业")

        sw_class_l2 = pro.index_classify(level='L2', src='SW2021')  # 二级分类
        print(f"  申万二级分类获取成功，共{len(sw_class_l2)}个行业")

        sw_class_l3 = pro.index_classify(level='L3', src='SW2021')  # 三级分类
        print(f"  申万三级分类获取成功，共{len(sw_class_l3)}个行业")

        return sw_class, sw_class_l2, sw_class_l3

    except Exception as e:
        print(f"  获取申万行业分类表出错: {e}")
        print("  注意：需要2000积分权限才能调用index_classify接口")
        print("  将使用备用方法获取行业信息...")
        return None, None, None

def get_stock_sw_industry(ts_code):
    """
    获取股票的申万行业分类信息
    """
    try:
        # 获取股票基本信息中的申万一级行业
        stock_info = pro.stock_basic(ts_code=ts_code, fields='ts_code,name,industry')
        
        if stock_info.empty:
            return None
            
        # 获取申万行业成分股信息来确定更详细的分类
        # 这个方法需要遍历各个申万指数来找到该股票所属的行业
        
        # 先获取申万分类表
        sw_l1, sw_l2, sw_l3 = get_sw_industry_classification()
        
        stock_industry = {
            'ts_code': ts_code,
            'name': stock_info.iloc[0]['name'],
            'sw_l1_name': stock_info.iloc[0]['industry'],  # stock_basic中的industry是申万一级
            'sw_l2_name': '',
            'sw_l3_name': ''
        }
        
        # 如果成功获取到申万分类表，尝试匹配更详细的分类
        if sw_l1 is not None and sw_l2 is not None and sw_l3 is not None:
            # 这里需要通过申万指数成分股接口来获取更详细的分类
            # 由于API限制和复杂性，这里先返回一级分类
            pass
            
        return stock_industry
        
    except Exception as e:
        print(f"获取股票申万分类出错: {e}")
        return None

def get_stock_industry_detail(ts_code):
    """
    获取股票详细行业信息（包括申万三级分类）
    使用多重方法确保获取到准确的申万行业信息
    """
    try:
        print(f"    获取{ts_code}的申万行业信息...")

        industry_info = {
            'sw_l1_name': '',
            'sw_l2_name': '',
            'sw_l3_name': '',
            'sw_l1_code': ''
        }

        # 方法1：从stock_basic获取申万一级行业（最可靠的方法）
        stock_info = pro.stock_basic(ts_code=ts_code, fields='ts_code,name,industry')

        if not stock_info.empty:
            industry_info['sw_l1_name'] = stock_info.iloc[0]['industry'] or ''
            print(f"    从stock_basic获取申万一级: {industry_info['sw_l1_name']}")

        # 方法2：通过申万行业指数验证和补充信息
        try:
            # 申万一级行业指数映射表（完整版）
            sw_l1_indices = {
                '801010.SI': '农林牧渔', '801020.SI': '采掘', '801030.SI': '化工',
                '801040.SI': '钢铁', '801050.SI': '有色金属', '801080.SI': '电子',
                '801110.SI': '家用电器', '801120.SI': '食品饮料', '801130.SI': '纺织服装',
                '801140.SI': '轻工制造', '801150.SI': '医药生物', '801160.SI': '公用事业',
                '801170.SI': '交通运输', '801180.SI': '房地产', '801200.SI': '商业贸易',
                '801210.SI': '休闲服务', '801230.SI': '综合', '801710.SI': '建筑材料',
                '801720.SI': '建筑装饰', '801730.SI': '电气设备', '801740.SI': '国防军工',
                '801750.SI': '计算机', '801760.SI': '传媒', '801770.SI': '通信',
                '801780.SI': '银行', '801790.SI': '非银金融', '801880.SI': '汽车',
                '801890.SI': '机械设备'
            }

            # 如果stock_basic中没有行业信息，或者需要验证，通过申万指数查找
            if not industry_info['sw_l1_name']:
                print(f"    stock_basic中无行业信息，通过申万指数查找...")

            for index_code, industry_name in sw_l1_indices.items():
                try:
                    members = pro.index_weight(index_code=index_code,
                                             fields='index_code,con_code,trade_date')

                    if not members.empty:
                        latest_date = members['trade_date'].max()
                        latest_members = members[members['trade_date'] == latest_date]

                        if ts_code in latest_members['con_code'].values:
                            industry_info['sw_l1_code'] = index_code
                            if not industry_info['sw_l1_name']:
                                industry_info['sw_l1_name'] = industry_name
                            print(f"    通过申万指数确认: {industry_name} ({index_code})")
                            break

                    time.sleep(0.05)  # 减少延迟但避免限频

                except Exception:
                    continue

        except Exception as e:
            print(f"    申万指数验证失败: {e}")

        # 方法3：尝试获取申万二三级分类（如果有权限）
        if industry_info['sw_l1_name']:
            try:
                # 尝试获取更详细的分类信息
                sw_l1, sw_l2, sw_l3 = get_sw_industry_classification()

                if sw_l2 is not None and not sw_l2.empty:
                    # 根据一级行业匹配二级行业
                    l1_name = industry_info['sw_l1_name']
                    l2_matches = sw_l2[sw_l2['parent_code'].str.contains(industry_info['sw_l1_code'][:6], na=False)]
                    if not l2_matches.empty:
                        # 这里可以添加更精确的匹配逻辑
                        print(f"    找到{len(l2_matches)}个二级分类选项")

            except Exception as e:
                print(f"    获取详细分类失败: {e}")

        return industry_info

    except Exception as e:
        print(f"    获取申万行业信息出错: {e}")
        return {'sw_l1_name': '', 'sw_l2_name': '', 'sw_l3_name': '', 'sw_l1_code': ''}

def get_financial_data(ts_code, start_date, end_date):
    """
    获取企业财务数据（已去重）
    """
    
    try:
        # 1. 获取现金流量表数据
        cashflow_data = pro.cashflow(ts_code=ts_code, 
                                    start_date=start_date, 
                                    end_date=end_date,
                                    fields='ts_code,ann_date,f_ann_date,end_date,n_cashflow_act')
        cashflow_data = clean_financial_data(cashflow_data)
        
        # 2. 获取利润表数据
        income_data = pro.income(ts_code=ts_code,
                                start_date=start_date,
                                end_date=end_date,
                                fields='ts_code,ann_date,f_ann_date,end_date,n_income')
        income_data = clean_financial_data(income_data)
        
        # 3. 获取资产负债表数据
        balance_data = pro.balancesheet(ts_code=ts_code,
                                       start_date=start_date,
                                       end_date=end_date,
                                       fields='ts_code,ann_date,f_ann_date,end_date,total_assets,total_hldr_eqy_exc_min_int,total_liab')
        balance_data = clean_financial_data(balance_data)
        
        return cashflow_data, income_data, balance_data
        
    except Exception as e:
        print(f"获取财务数据时出错: {e}")
        return None, None, None

def get_comprehensive_stock_data(ts_code, start_date, end_date=None):
    """
    获取综合股票数据，包含所有要求的字段
    """
    if end_date is None:
        end_date = datetime.now().strftime('%Y%m%d')
    
    print(f"正在获取{ts_code}的综合数据...")
    
    # 1. 获取股票基本信息
    print("  获取基本信息...")
    stock_info = get_stock_basic_info(ts_code)
    
    # 2. 获取交易数据
    print("  获取交易数据...")
    trading_data = get_trading_data(ts_code, start_date, end_date)
    
    # 3. 获取资金流向数据
    print("  获取资金流向数据...")
    money_flow_data = get_money_flow_data(ts_code, start_date, end_date)
    
    # 4. 获取财务数据
    print("  获取财务数据...")
    cashflow_data, income, balance = get_financial_data(ts_code, start_date, end_date)
    
    # 5. 获取指数成分股信息
    print("  获取指数成分股信息...")
    index_members = get_index_members()
    
    # 6. 获取行业分类
    print("  获取行业分类...")
    industry_info = get_stock_industry_detail(ts_code)
    
    # 开始合并数据
    print("  合并数据...")
    
    if trading_data is None or trading_data.empty:
        print("交易数据为空，无法继续")
        return None
    
    # 以交易数据为基础
    result_data = trading_data.copy()
    
    # 添加股票名称
    if stock_info is not None:
        result_data['股票名称'] = stock_info['name']
    else:
        result_data['股票名称'] = ''
    
    # 重命名列名
    column_mapping = {
        'ts_code': '股票代码',
        'trade_date': '交易日期',
        'open': '开盘价',
        'high': '最高价',
        'low': '最低价',
        'close': '收盘价',
        'pre_close': '前收盘价',
        'vol': '成交量',
        'amount': '成交额',
        'circ_mv': '流通市值',
        'total_mv': '总市值'
    }
    
    result_data.rename(columns=column_mapping, inplace=True)
    
    # 合并资金流向数据
    if money_flow_data is not None and not money_flow_data.empty:
        money_flow_renamed = money_flow_data.rename(columns={
            'buy_sm_amount': '散户资金买入额',
            'sell_sm_amount': '散户资金卖出额',
            'buy_md_amount': '中户资金买入额',
            'sell_md_amount': '中户资金卖出额',
            'buy_lg_amount': '大户资金买入额',
            'sell_lg_amount': '大户资金卖出额',
            'buy_elg_amount': '机构资金买入额',
            'sell_elg_amount': '机构资金卖出额'
        })
        
        result_data = pd.merge(result_data, 
                             money_flow_renamed[['ts_code', 'trade_date', '中户资金买入额', '中户资金卖出额',
                                               '大户资金买入额', '大户资金卖出额', '散户资金买入额', '散户资金卖出额',
                                               '机构资金买入额', '机构资金卖出额']], 
                             left_on=['股票代码', '交易日期'], 
                             right_on=['ts_code', 'trade_date'], 
                             how='left')
        result_data.drop(['ts_code_y', 'trade_date_y'], axis=1, errors='ignore', inplace=True)
        result_data.rename(columns={'ts_code_x': '股票代码', 'trade_date_x': '交易日期'}, inplace=True)
    
    # 添加财务数据（取最近的季报数据）
    financial_columns = ['净资产', '总资产', '总负债', '净利润(当季)']
    for col in financial_columns:
        result_data[col] = None
    
    if balance is not None and not balance.empty:
        latest_balance = balance.iloc[0]
        result_data['净资产'] = latest_balance.get('total_hldr_eqy_exc_min_int')
        result_data['总资产'] = latest_balance.get('total_assets')
        result_data['总负债'] = latest_balance.get('total_liab')
    
    if income is not None and not income.empty:
        latest_income = income.iloc[0]
        result_data['净利润(当季)'] = latest_income.get('n_income')
    
    # 添加指数成分股标识
    index_columns = ['沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', '创业板指成分股']
    for col in index_columns:
        result_data[col] = 0
    
    for index_name, members in index_members.items():
        if ts_code in members:
            col_name = f"{index_name}成分股"
            if col_name in result_data.columns:
                result_data[col_name] = 1
    
    # 添加申万行业分类（包含代码和名称）
    result_data['新版申万一级行业代码'] = industry_info.get('sw_l1_code', '') if industry_info else ''
    result_data['新版申万一级行业名称'] = industry_info.get('sw_l1_name', '') if industry_info else ''
    result_data['新版申万二级行业代码'] = industry_info.get('sw_l2_code', '') if industry_info else ''
    result_data['新版申万二级行业名称'] = industry_info.get('sw_l2_name', '') if industry_info else ''
    result_data['新版申万三级行业代码'] = industry_info.get('sw_l3_code', '') if industry_info else ''
    result_data['新版申万三级行业名称'] = industry_info.get('sw_l3_name', '') if industry_info else ''
    
    # 调整列顺序
    desired_columns = [
        '股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '前收盘价',
        '成交量', '成交额', '流通市值', '总市值', '净资产', '总资产', '总负债', '净利润(当季)',
        '中户资金买入额', '中户资金卖出额', '大户资金买入额', '大户资金卖出额',
        '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额',
        '沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', '创业板指成分股',
        '新版申万一级行业代码', '新版申万一级行业名称', '新版申万二级行业代码', '新版申万二级行业名称',
        '新版申万三级行业代码', '新版申万三级行业名称'
    ]
    
    # 确保所有列都存在，不存在的列填充为空值
    for col in desired_columns:
        if col not in result_data.columns:
            result_data[col] = None
    
    result_data = result_data[desired_columns]
    
    return result_data

def analyze_comprehensive_data(df, data_name):
    """
    分析综合数据质量
    """
    if df is None or df.empty:
        print(f"{data_name}: 数据为空")
        return
    
    print(f"\n=== {data_name} 数据质量分析 ===")
    print(f"总记录数: {len(df)}")
    print(f"交易日期范围: {df['交易日期'].min()} 到 {df['交易日期'].max()}")
    print(f"数据完整性:")
    
    key_columns = ['开盘价', '收盘价', '成交量', '成交额', '中户资金买入额', '净资产', '新版申万一级行业名称']
    for col in key_columns:
        if col in df.columns:
            if col == '新版申万一级行业名称':
                # 对于申万行业，检查非空且非空字符串的记录
                valid_count = df[col].notna().sum() - (df[col] == '').sum()
                print(f"  {col}: {valid_count}/{len(df)} 条记录有数据 ({valid_count/len(df)*100:.1f}%)")
                if valid_count > 0:
                    unique_industries = df[df[col].notna() & (df[col] != '')][col].unique()
                    print(f"    行业类别: {', '.join(unique_industries)}")
            else:
                null_count = df[col].isnull().sum()
                print(f"  {col}: {len(df) - null_count}/{len(df)} 条记录有数据 ({(len(df) - null_count)/len(df)*100:.1f}%)")

# 使用示例
if __name__ == "__main__":
    # 以平安银行为例
    ts_code = '000001.SZ'
    start_date = '20240101'  # 获取2024年的数据
    end_date = '20241231'
    
    print("正在获取综合股票数据...")
    
    # 获取综合数据
    comprehensive_data = get_comprehensive_stock_data(ts_code, start_date, end_date)
    
    if comprehensive_data is not None:
        analyze_comprehensive_data(comprehensive_data, "综合股票数据")
        
        print("\n=== 综合数据预览 ===")
        print(comprehensive_data.head(5))
        
        print(f"\n=== 申万行业信息检查 ===")
        sw_l1_name = comprehensive_data['新版申万一级行业名称'].iloc[0] if not comprehensive_data.empty else ''
        sw_l1_code = comprehensive_data['新版申万一级行业代码'].iloc[0] if not comprehensive_data.empty else ''
        print(f"申万一级行业: {sw_l1_name} ({sw_l1_code})")

        print(f"\n=== 数据统计信息 ===")
        print(f"数据形状: {comprehensive_data.shape}")
        print(f"列名: {list(comprehensive_data.columns)}")

        # 检查申万行业数据质量
        sw_data_quality = comprehensive_data['新版申万一级行业名称'].notna().sum() - (comprehensive_data['新版申万一级行业名称'] == '').sum()
        print(f"申万行业数据完整性: {sw_data_quality}/{len(comprehensive_data)} ({sw_data_quality/len(comprehensive_data)*100:.1f}%)")
        
        # 保存到Excel
        try:
            filename = f'comprehensive_stock_data_{ts_code.replace(".", "_")}.xlsx'
            comprehensive_data.to_excel(filename, index=False)
            print(f"\n数据已保存到 {filename} 文件")
            print(f"共保存 {len(comprehensive_data)} 条记录，{len(comprehensive_data.columns)} 个字段")
            
            # 显示样本数据
            print(f"\n=== 样本数据展示 ===")
            pd.set_option('display.max_columns', None)
            pd.set_option('display.width', None)
            print(comprehensive_data.head(3))
            
        except Exception as e:
            print(f"保存文件时出错: {e}")
    else:
        print("获取综合数据失败")