
# 导入tushare
import tushare as ts
# 初始化pro接口
pro = ts.pro_api('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')

# 拉取数据
df = pro.index_weight(**{
    "index_code": "932000.CSI",
    "trade_date": '',
    "start_date": "",
    "end_date": "",
    "ts_code": "",
    "limit": "",
    "offset": ""
}, fields=[
    "index_code",
    "con_code",
    "trade_date",
    "weight"
])
# 打印DataFrame的前5行以确认数据已加载
print(df.head())

# 将数据保存为CSV文件
df.to_csv('指数.csv', index=False, encoding='utf-8-sig')

print("数据已成功保存到 '指数.csv' 文件中。")
        